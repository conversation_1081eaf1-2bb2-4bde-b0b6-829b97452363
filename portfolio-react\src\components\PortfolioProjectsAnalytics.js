import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrow<PERSON>eft, FaFolder, FaMousePointer, FaClock, FaUsers, FaChartLine, FaExternalLinkAlt, FaExclamationTriangle, FaCheckCircle, FaImage, FaLink } from 'react-icons/fa';
import './PortfolioProjectsAnalytics.css';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';
import { PORTFOLIO_PROJECTS, findPortfolioProject, isValidPortfolioProject } from '../config/projectsConfig';

// Portfolio items data (matching the Portfolio component)
const portfolioItems = [
  { // ID : 1
    href: "https://threed-e-commerce.onrender.com",
    image: "/3D E-Comm.PNG",
    alt: "3D Ecommerce",
    title: "3D Ecommerce",
    forSale: true,
    id: 1
  },
  { // ID : 2
    href: "#",
    image: "/ex1.webp",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false,
    id: 2
  },
  { // ID : 3
    href: "https://creative-website-jumper.onrender.com",
    image: "/P1.PNG",
    alt: "Nexit Brand Identity",
    title: "Professional Portfolio",
    forSale: true,
    id: 3
  },
  { // ID : 4
    href: "#",
    image: "/ex3.webp",
    alt: "Will be deployed soon.s",
    title: "Will be deployed soon.",
    forSale: false,
    id: 4
  },
  { // ID : 5
    href: "https://flawless-2pqq.onrender.com",
    image: "/Flaw.PNG",
    alt: "Flaw",
    title: "Available",
    forSale: true,
    id: 5
  },
  { // ID : 6
    href: "#",
    image: "/ex5.png",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false,
    id: 6
  },
  { // ID : 7
    href: "https://hoobank-neon-future.onrender.com",
    image: "/HooBank.png",
    alt: "Business Web UI",
    title: "Experience digital banking with AI ",
    forSale: true,
    id: 7
  }
];

const PortfolioProjectsAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'available', 'unavailable'
  const navigate = useNavigate();

  // Helper function to get portfolio item data using centralized project configuration
  const getPortfolioItemData = (projectId, projectTitle) => {
    // Use centralized configuration to find the project
    const portfolioProject = findPortfolioProject(projectId, projectTitle);

    if (portfolioProject) {
      // Return the project data from configuration
      return {
        title: portfolioProject.title,
        href: portfolioProject.href,
        forSale: portfolioProject.forSale,
        image: portfolioItems[portfolioProject.index]?.image || '/default-project.png',
        alt: portfolioItems[portfolioProject.index]?.alt || portfolioProject.title
      };
    }

    // Fallback to original logic if not found in configuration
    if (projectId && projectId.includes('portfolio-')) {
      const parts = projectId.split('-');
      if (parts.length >= 2) {
        const index = parseInt(parts[1]);
        if (!isNaN(index) && index < portfolioItems.length) {
          return portfolioItems[index];
        }
      }
    }

    if (projectTitle) {
      return portfolioItems.find(item =>
        item.title.toLowerCase().includes(projectTitle.toLowerCase()) ||
        projectTitle.toLowerCase().includes(item.title.toLowerCase())
      );
    }

    return null;
  };

  useEffect(() => {
    fetchAnalytics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      // Wake up backend before attempting to fetch analytics
      console.log('Waking up backend...');
      await preemptiveWakeup();

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/admin/login');
        return;
      }

      console.log('Fetching portfolio analytics from:', API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS);

      const response = await safeFetch(API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.status === 401) {
        localStorage.removeItem('token');
        navigate('/admin/login');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to fetch analytics (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('Analytics data received:', data);

      // Additional frontend filtering using centralized project configuration
      if (data.success && data.data) {
        const filterProjects = (projects) => {
          return projects.filter(project => {
            return isValidPortfolioProject(project.projectId, project.projectTitle);
          }).slice(0, 7); // Ensure maximum of 7 projects
        };

        const filteredData = {
          ...data,
          data: filterProjects(data.data),
          availableProjects: data.availableProjects ? filterProjects(data.availableProjects) : [],
          unavailableProjects: data.unavailableProjects ? filterProjects(data.unavailableProjects) : []
        };

        console.log('Filtered portfolio projects:', filteredData.data.length);
        setAnalytics(filteredData);
      } else {
        setAnalytics(data);
      }
    } catch (err) {
      // Don't show errors for extension-related issues
      if (!isExtensionError(err)) {
        console.error('Analytics fetch error:', err);
        setError(`Error loading analytics: ${err.message}`);
      } else {
        console.log('Extension error suppressed in PortfolioProjectsAnalytics:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (projectUrl) => {
    if (projectUrl && projectUrl !== '' && projectUrl !== '#') {
      window.open(projectUrl, '_blank');
    }
  };

  const handleBackClick = () => {
    navigate('/admin/dashboard');
  };

  const getDisplayData = () => {
    if (!analytics) return [];
    
    switch (activeTab) {
      case 'available':
        return analytics.availableProjects || [];
      case 'unavailable':
        return analytics.unavailableProjects || [];
      default:
        return analytics.data || [];
    }
  };

  if (loading) {
    return (
      <div className="portfolio-analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Portfolio Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-analytics-container">
      <div className="analytics-header">
        <button onClick={handleBackClick} className="back-button">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1><FaFolder /> Portfolio Projects Analytics</h1>
        <p>Detailed analytics for portfolio carousel project interactions</p>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="analytics-summary">
            <div className="summary-card">
              <FaFolder className="summary-icon" />
              <div className="summary-content">
                <h3>Total Projects</h3>
                <div className="summary-value">{analytics.totalProjects}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaMousePointer className="summary-icon" />
              <div className="summary-content">
                <h3>Total Interactions</h3>
                <div className="summary-value">{analytics.summary.totalInteractions}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaClock className="summary-icon" />
              <div className="summary-content">
                <h3>Total Time</h3>
                <div className="summary-value">{formatDuration(analytics.summary.totalDuration)}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaChartLine className="summary-icon" />
              <div className="summary-content">
                <h3>Avg Interactions/Project</h3>
                <div className="summary-value">{analytics.summary.avgInteractionsPerProject}</div>
              </div>
            </div>
          </div>

          {/* Availability Summary */}
          <div className="availability-summary">
            <div className="availability-card available">
              <FaCheckCircle className="availability-icon" />
              <div className="availability-content">
                <h3>Available Projects</h3>
                <div className="availability-value">{analytics.summary.availableProjectsCount}</div>
              </div>
            </div>
            <div className="availability-card unavailable">
              <FaExclamationTriangle className="availability-icon" />
              <div className="availability-content">
                <h3>Unavailable Projects</h3>
                <div className="availability-value">{analytics.summary.unavailableProjectsCount}</div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="tab-navigation">
            <button 
              className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => setActiveTab('all')}
            >
              All Projects ({analytics.totalProjects})
            </button>
            <button 
              className={`tab-button ${activeTab === 'available' ? 'active' : ''}`}
              onClick={() => setActiveTab('available')}
            >
              Available ({analytics.summary.availableProjectsCount})
            </button>
            <button 
              className={`tab-button ${activeTab === 'unavailable' ? 'active' : ''}`}
              onClick={() => setActiveTab('unavailable')}
            >
              Unavailable ({analytics.summary.unavailableProjectsCount})
            </button>
          </div>

          {/* Projects List */}
          <div className="projects-analytics-list">
            {getDisplayData().length > 0 ? (
              <div className="projects-grid">
                {getDisplayData().map((project, index) => {
                  const portfolioData = getPortfolioItemData(project.projectId, project.projectTitle);

                  return (
                    <div key={index} className={`project-analytics-card ${project.availabilityStatus === false ? 'unavailable' : 'available'}`}>
                      <div className="project-visual-header">
                        {portfolioData && portfolioData.image && (
                          <div className="project-image-container">
                            <img
                              src={portfolioData.image}
                              alt={portfolioData.alt || portfolioData.title}
                              className="project-image-analytics"
                              onError={(e) => {
                                e.target.style.display = 'none';
                              }}
                            />
                            {portfolioData.forSale && (
                              <div className="for-sale-badge">
                                <span>Available</span>
                              </div>
                            )}
                          </div>
                        )}

                        <div className="project-header">
                          <h3 className="project-title">{project.projectTitle}</h3>

                          <div className="project-meta">
                            <div className="project-status">
                              {project.availabilityStatus !== false ? (
                                <span className="status-badge available">
                                  <FaCheckCircle /> Available
                                </span>
                              ) : (
                                <span className="status-badge unavailable">
                                  <FaExclamationTriangle /> Unavailable
                                </span>
                              )}
                            </div>

                            {portfolioData && portfolioData.href && portfolioData.href !== '#' && (
                              <div className="project-link-info">
                                <FaLink className="link-icon" />
                                <span className="link-text">{portfolioData.href}</span>
                              </div>
                            )}
                          </div>

                          {project.projectUrl && project.projectUrl !== '' && project.projectUrl !== '#' && (
                            <button
                              className="view-project-btn"
                              onClick={() => handleProjectClick(project.projectUrl)}
                            >
                              <FaExternalLinkAlt /> View Project
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="project-stats">
                        <div className="stats-grid">
                          <div className="stat-item primary">
                            <FaMousePointer className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.totalInteractions}</span>
                              <span className="stat-label">Total Interactions</span>
                            </div>
                          </div>
                          <div className="stat-item primary">
                            <FaUsers className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{project.uniqueVisitorCount}</span>
                              <span className="stat-label">Unique Visitors</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.totalDuration)}</span>
                              <span className="stat-label">Total Duration</span>
                            </div>
                          </div>
                          <div className="stat-item secondary">
                            <FaClock className="stat-icon" />
                            <div className="stat-content">
                              <span className="stat-value">{formatDuration(project.avgDuration)}</span>
                              <span className="stat-label">Avg Duration</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="project-details">
                        <div className="detail-item">
                          <strong>Project ID:</strong>
                          <span className="detail-value">{project.projectId || 'N/A'}</span>
                        </div>
                        <div className="detail-item">
                          <strong>Last Interaction:</strong>
                          <span className="detail-value">
                            {project.lastInteraction ?
                              new Date(project.lastInteraction).toLocaleString() :
                              'Never'
                            }
                          </span>
                        </div>
                        {portfolioData && portfolioData.alt && (
                          <div className="detail-item">
                            <strong>Description:</strong>
                            <span className="detail-value">{portfolioData.alt}</span>
                          </div>
                        )}
                      </div>

                      {project.interactionTypes && project.interactionTypes.length > 0 && (
                        <div className="interaction-types">
                          <h4>Interaction Types</h4>
                          <div className="types-list">
                            {project.interactionTypes.map((type, idx) => (
                              <span key={idx} className="interaction-type-badge">{type}</span>
                            ))}
                          </div>
                        </div>
                      )}

                      {project.recentInteractions && project.recentInteractions.length > 0 && (
                        <div className="recent-interactions">
                          <h4>Recent Interactions</h4>
                          <div className="interactions-list">
                            {project.recentInteractions.slice(0, 5).map((interaction, idx) => (
                              <div key={idx} className="interaction-item">
                                <div className="interaction-info">
                                  <span className="interaction-ip">{interaction.ip}</span>
                                  <span className="interaction-type">{interaction.interactionType || 'view'}</span>
                                </div>
                                <div className="interaction-meta">
                                  <span className="interaction-time">
                                    {new Date(interaction.timestamp).toLocaleString()}
                                  </span>
                                  <span className="interaction-duration">
                                    {formatDuration(interaction.duration || 0)}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="no-data">
                <p>No portfolio project analytics data available for this category.</p>
                <p>Data will appear once users start interacting with portfolio projects.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default PortfolioProjectsAnalytics;
