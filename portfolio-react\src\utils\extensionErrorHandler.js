/**
 * Browser Extension Error Handler
 * 
 * This utility handles common browser extension errors that can interfere
 * with React applications, particularly the intermittent async listener errors.
 */

import { logDebug } from './logger';

// Common browser extension error patterns - EXPANDED
const EXTENSION_ERROR_PATTERNS = [
  'Could not establish connection',
  'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received',
  'Extension context invalidated',
  'The message port closed before a response was received',
  'chrome-extension://',
  'moz-extension://',
  'safari-extension://',
  'edge-extension://',
  'Receiving end does not exist',
  'Could not establish connection. Receiving end does not exist',
  'The extensions gallery cannot be scripted',
  'Script error for: chrome-extension',
  'Non-Error promise rejection captured',
  'Message channel closed',
  'Port disconnected',
  'Extension was invalidated',
  'Cannot access chrome',
  'chrome.runtime is not available',
  'browser.runtime is not available',
  'Invocation of form runtime.connect',
  'Error in invocation of runtime.sendMessage',
  'Could not load background script',
  'Extension manifest is not valid',
  'This extension may have been corrupted',
  'Unchecked runtime.lastError',
  'The tab was closed',
  'No tab with id',
  'Cannot access a chrome',
  'Cannot access contents of',
  'Script injected by extension',
  'Content script error',
  'Background script error',
  'Popup script error',
  // Additional patterns for async listener errors
  'message channel closed before a response was received',
  'listener indicated an asynchronous response',
  'async response by returning true',
  'channel closed before',
  'response was received',
  // Flag-related extension errors
  '🏁 FLAG DEBUG',
  '🏁 FLAG RESULT',
  'extensionErrorHandler.js',
  // React Scheduler violations and performance warnings - EXPANDED
  '[Violation]',
  'scheduler.development.js',
  'message\' handler took',
  'handler took',
  'Violation',
  'setTimeout\' handler took',
  'Forced reflow while executing JavaScript took',
  'reflow while executing',
  // Additional performance violation patterns
  'Long running JavaScript task took',
  'Long task took',
  'Task took',
  'ms to complete',
  'Performance violation',
  'Slow network resource',
  'Slow synchronous XMLHttpRequest',
  'Synchronous XMLHttpRequest',
  'Main thread blocked',
  'Frame dropped',
  'Input handling delayed',
  'Timer handler took',
  'Event handler took',
  'Promise handler took',
  'Callback took',
  'Function took',
  'Script took',
  'Layout took',
  'Paint took',
  'Composite took',
  'Update took',
  // Browser Privacy Sandbox / Topics API messages
  'Browsing Topics API',
  'Topics API removed',
  'Topics API not available',
  'Privacy Sandbox',
  'Interest cohort',
  'FLoC',
  'FLEDGE',
  'Attribution Reporting API',
  'Trust Tokens',
  'which is main frame'
];

/**
 * Check if an error is related to browser extensions
 * @param {string|Error} error - The error message or Error object
 * @returns {boolean} - True if it's an extension-related error
 */
export const isExtensionError = (error) => {
  const message = typeof error === 'string' ? error : (error?.message || error?.toString() || '');
  return EXTENSION_ERROR_PATTERNS.some(pattern => message.includes(pattern));
};

/**
 * Safely handle potential extension errors
 * @param {Function} callback - The function to execute
 * @param {string} context - Context description for logging
 * @returns {Promise|any} - The result of the callback or null if extension error
 */
export const safeExecute = async (callback, context = 'unknown') => {
  try {
    return await callback();
  } catch (error) {
    if (isExtensionError(error)) {
      logDebug(`Extension error suppressed in ${context}:`, error.message);
      return null;
    }
    // Re-throw non-extension errors
    throw error;
  }
};

/**
 * Wrap fetch calls to handle extension-related network errors
 * @param {string} url - The URL to fetch
 * @param {object} options - Fetch options
 * @returns {Promise<Response>} - The fetch response
 */
export const safeFetch = async (url, options = {}) => {
  try {
    // Add a timeout to prevent hanging requests that might be caused by extensions
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000);

    const fetchOptions = {
      ...options,
      signal: options.signal || controller.signal
    };

    const response = await fetch(url, fetchOptions);
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    // Handle AbortError from timeout
    if (error.name === 'AbortError' && !options.signal) {
      logDebug('Fetch timeout - possibly due to extension interference');
      return new Response(null, { status: 408, statusText: 'Request Timeout' });
    }

    // Handle extension errors
    if (isExtensionError(error)) {
      logDebug('Extension error during fetch suppressed:', error.message);
      return new Response(null, { status: 0, statusText: 'Extension Error Suppressed' });
    }

    // Re-throw other errors
    throw error;
  }
};

/**
 * Initialize global extension error handlers
 * This should be called once at app startup
 */
export const initializeExtensionErrorHandling = () => {
  // Enhanced error event handler
  const handleError = (event) => {
    const message = event.message || event.error?.message || event.error?.toString() || '';
    const filename = event.filename || '';
    const source = event.source || '';

    // Check if error is from extension or contains extension patterns
    if (isExtensionError(message) ||
        filename.includes('extension://') ||
        source.includes('extension://') ||
        message.includes('chrome-extension') ||
        message.includes('moz-extension') ||
        message.includes('safari-extension')) {
      logDebug('Extension error suppressed:', message);
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  };

  // Enhanced unhandled rejection handler
  const handleUnhandledRejection = (event) => {
    const reason = event.reason;
    let message = '';

    if (reason) {
      if (typeof reason === 'string') {
        message = reason;
      } else if (reason.message) {
        message = reason.message;
      } else if (reason.toString) {
        message = reason.toString();
      }
    }

    if (isExtensionError(message)) {
      logDebug('Extension promise rejection suppressed:', message);
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  };

  // Add event listeners if not already added
  if (!window.__extensionErrorHandlersAdded) {
    // Use capture phase to catch errors before they bubble
    window.addEventListener('error', handleError, true);
    window.addEventListener('unhandledrejection', handleUnhandledRejection, true);

    // Also add to document for broader coverage
    document.addEventListener('error', handleError, true);
    document.addEventListener('unhandledrejection', handleUnhandledRejection, true);

    window.__extensionErrorHandlersAdded = true;
    logDebug('Extension error handlers initialized with enhanced coverage');
  }
};

/**
 * Wrap console methods to filter extension errors
 */
export const filterExtensionConsoleErrors = () => {
  if (window.__consoleFiltersApplied) return;

  const originalError = console.error;
  const originalWarn = console.warn;
  const originalLog = console.log;

  console.error = (...args) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    // Filter out extension errors, violations, and performance warnings
    if (!isExtensionError(message) &&
        !message.includes('[Violation]') &&
        !message.includes('setTimeout\' handler took') &&
        !message.includes('Forced reflow while executing JavaScript took')) {
      originalError.apply(console, args);
    } else {
      // Silently suppress extension errors and performance violations
    }
  };

  console.warn = (...args) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    // Filter out extension errors, scheduler violations, and performance warnings
    if (!isExtensionError(message) &&
        !message.includes('[Violation]') &&
        !message.includes('scheduler.development.js') &&
        !message.includes('setTimeout\' handler took') &&
        !message.includes('Forced reflow while executing JavaScript took') &&
        !message.includes('reflow while executing')) {
      originalWarn.apply(console, args);
    } else {
      // Silently suppress extension warnings, scheduler violations, and performance warnings
    }
  };

  // Also filter console.log for extension errors and scheduler violations
  console.log = (...args) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    // Filter out extension errors, flag debug messages, and scheduler violations (but allow backend messages)
    if (!isExtensionError(message) &&
        !message.includes('🏁 FLAG DEBUG') &&
        !message.includes('🏁 FLAG RESULT') &&
        !message.includes('extensionErrorHandler.js') &&
        !message.includes('[Violation]') &&
        !message.includes('scheduler.development.js') &&
        !message.includes('setTimeout\' handler took') &&
        !message.includes('Forced reflow while executing JavaScript took')) {
      originalLog.apply(console, args);
    } else {
      // Silently suppress these messages - don't even log them as debug
      // This prevents console spam from violations and extension errors
    }
  };

  // Override console.info as well to catch any remaining violations
  const originalInfo = console.info;
  console.info = (...args) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    if (!isExtensionError(message) &&
        !message.includes('[Violation]') &&
        !message.includes('setTimeout\' handler took') &&
        !message.includes('Forced reflow while executing JavaScript took')) {
      originalInfo.apply(console, args);
    }
  };

  // Override console.debug as well
  const originalDebug = console.debug;
  console.debug = (...args) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    if (!isExtensionError(message) &&
        !message.includes('[Violation]') &&
        !message.includes('setTimeout\' handler took') &&
        !message.includes('Forced reflow while executing JavaScript took')) {
      originalDebug.apply(console, args);
    }
  };

  // Add a more aggressive violation filter by overriding the original console methods
  // This catches violations that might be logged directly by React or other libraries
  const createViolationFilter = (originalMethod) => {
    return function(...args) {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      // Enhanced violation pattern matching
      const violationPatterns = [
        '[Violation]',
        'setTimeout\' handler took',
        'Forced reflow while executing JavaScript took',
        'handler took',
        'ms to complete',
        'Long running JavaScript task took',
        'Long task took',
        'Task took',
        'Performance violation',
        'Main thread blocked',
        'Frame dropped',
        'Input handling delayed',
        'Timer handler took',
        'Event handler took',
        'Promise handler took',
        'Callback took',
        'Function took',
        'Script took',
        'Layout took',
        'Paint took',
        'Composite took',
        'Update took',
        'scheduler.development.js',
        'message\' handler took'
      ];

      // Check for any violation patterns
      const hasViolation = violationPatterns.some(pattern =>
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      if (hasViolation) {
        // Completely suppress violation messages
        return;
      }

      // Call original method for non-violation messages
      return originalMethod.apply(console, args);
    };
  };

  // Apply the violation filter to all console methods
  console.log = createViolationFilter(originalLog);
  console.warn = createViolationFilter(originalWarn);
  console.error = createViolationFilter(originalError);
  console.info = createViolationFilter(originalInfo);
  console.debug = createViolationFilter(originalDebug);

  window.__consoleFiltersApplied = true;
  logDebug('Console error filtering enabled with aggressive violation suppression');
};

/**
 * Create a promise wrapper that ignores extension errors
 * @param {Promise} promise - The promise to wrap
 * @param {any} fallbackValue - Value to return if extension error occurs
 * @returns {Promise} - Wrapped promise
 */
export const extensionSafePromise = (promise, fallbackValue = null) => {
  return promise.catch(error => {
    if (isExtensionError(error)) {
      logDebug('Extension error in promise suppressed:', error.message);
      return fallbackValue;
    }
    throw error;
  });
};

export default {
  isExtensionError,
  safeExecute,
  safeFetch,
  initializeExtensionErrorHandling,
  filterExtensionConsoleErrors,
  extensionSafePromise
};
