/**
 * Performance Optimizer Utilities
 * Helps prevent console violations by optimizing heavy operations
 */

/**
 * Process data in chunks to prevent blocking the main thread
 * @param {Array} data - Array of data to process
 * @param {Function} processor - Function to process each chunk
 * @param {number} chunkSize - Size of each chunk (default: 50)
 * @returns {Promise} - Promise that resolves when all chunks are processed
 */
export const processInChunks = async (data, processor, chunkSize = 50) => {
  if (!Array.isArray(data) || data.length === 0) return [];
  
  const results = [];
  
  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize);
    
    // Process chunk in next animation frame to prevent blocking
    const chunkResult = await new Promise(resolve => {
      requestAnimationFrame(() => {
        try {
          const result = processor(chunk, i);
          resolve(result);
        } catch (error) {
          console.error('Error processing chunk:', error);
          resolve(null);
        }
      });
    });
    
    if (chunkResult !== null) {
      if (Array.isArray(chunkResult)) {
        results.push(...chunkResult);
      } else {
        results.push(chunkResult);
      }
    }
  }
  
  return results;
};

/**
 * Defer execution using requestIdleCallback or setTimeout fallback
 * @param {Function} callback - Function to execute
 * @param {Object} options - Options for requestIdleCallback
 * @returns {Promise} - Promise that resolves when callback is executed
 */
export const deferExecution = (callback, options = {}) => {
  return new Promise(resolve => {
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        try {
          const result = callback();
          resolve(result);
        } catch (error) {
          console.error('Error in deferred execution:', error);
          resolve(null);
        }
      }, { timeout: 5000, ...options });
    } else {
      setTimeout(() => {
        try {
          const result = callback();
          resolve(result);
        } catch (error) {
          console.error('Error in deferred execution:', error);
          resolve(null);
        }
      }, 0);
    }
  });
};

/**
 * Throttle function execution to prevent excessive calls
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Throttled function
 */
export const throttle = (func, delay) => {
  let timeoutId;
  let lastExecTime = 0;
  
  return function (...args) {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func.apply(this, args);
      lastExecTime = currentTime;
    } else {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func.apply(this, args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

/**
 * Debounce function execution to prevent excessive calls
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, delay) => {
  let timeoutId;
  
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
};

/**
 * Break up heavy synchronous operations into smaller async chunks
 * @param {Function} operation - Heavy operation to break up
 * @param {number} maxExecutionTime - Maximum execution time per chunk (ms)
 * @returns {Promise} - Promise that resolves when operation is complete
 */
export const breakUpHeavyOperation = async (operation, maxExecutionTime = 16) => {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    let result;
    
    const executeChunk = () => {
      const chunkStartTime = performance.now();
      
      try {
        // Execute operation until time limit is reached
        while (performance.now() - chunkStartTime < maxExecutionTime) {
          result = operation();
          if (result === 'COMPLETE') break;
        }
        
        if (result === 'COMPLETE') {
          resolve(result);
        } else {
          // Continue in next frame
          requestAnimationFrame(executeChunk);
        }
      } catch (error) {
        reject(error);
      }
    };
    
    executeChunk();
  });
};

/**
 * Optimize array operations to prevent blocking
 * @param {Array} array - Array to process
 * @param {Function} operation - Operation to perform on each item
 * @param {number} chunkSize - Size of each processing chunk
 * @returns {Promise<Array>} - Promise that resolves with processed array
 */
export const optimizedArrayOperation = async (array, operation, chunkSize = 100) => {
  if (!Array.isArray(array)) return [];
  
  const results = [];
  
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    
    await new Promise(resolve => {
      requestAnimationFrame(() => {
        try {
          const chunkResults = chunk.map(operation);
          results.push(...chunkResults);
          resolve();
        } catch (error) {
          console.error('Error in optimized array operation:', error);
          resolve();
        }
      });
    });
  }
  
  return results;
};

export default {
  processInChunks,
  deferExecution,
  throttle,
  debounce,
  breakUpHeavyOperation,
  optimizedArrayOperation
};