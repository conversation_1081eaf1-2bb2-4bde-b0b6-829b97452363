/* Locked Job Notification Styles */
.locked-job-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  animation: lockedJobFadeIn 0.3s ease-out;
}

.locked-job-content {
  background: linear-gradient(135deg, rgba(75, 0, 130, 0.95), rgba(255, 45, 85, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  cursor: default;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: lockedJobSlideIn 0.4s ease-out;
  text-align: center;
}

.locked-job-icon {
  font-size: 60px;
  margin-bottom: 20px;
  animation: lockedJobPulse 2s infinite;
}

.locked-job-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 20px 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.locked-job-message {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 25px;
  text-align: left;
}

.locked-job-message strong {
  color: #FF2D55;
  font-weight: 600;
}

.locked-job-details {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.locked-job-details h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 15px 0;
  text-align: left;
}

.locked-job-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.locked-job-details li {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.locked-job-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-align: left;
}

.locked-job-details li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 0;
}

.locked-job-contact {
  text-align: center;
}

.locked-job-contact p {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  font-weight: 500;
}

.locked-job-close {
  background: linear-gradient(135deg, #FF2D55, #4B0082);
  color: #FFFFFF;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);
}

.locked-job-close:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 45, 85, 0.4);
  background: linear-gradient(135deg, #FF2D55, #6A0DAD);
}

.locked-job-close:active {
  transform: translateY(0);
}

/* Animations */
@keyframes lockedJobFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes lockedJobSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes lockedJobPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .locked-job-content {
    padding: 30px 25px;
    margin: 20px;
    width: calc(100vw - 40px);
  }

  .locked-job-title {
    font-size: 24px;
  }

  .locked-job-message {
    font-size: 15px;
  }

  .locked-job-details h3 {
    font-size: 16px;
  }

  .locked-job-details li {
    font-size: 13px;
  }

  .locked-job-icon {
    font-size: 50px;
  }
}

@media (max-width: 480px) {
  .locked-job-content {
    padding: 25px 20px;
    margin: 15px;
    width: calc(100vw - 30px);
  }

  .locked-job-title {
    font-size: 20px;
    letter-spacing: 1px;
  }

  .locked-job-message {
    font-size: 14px;
  }

  .locked-job-close {
    padding: 10px 25px;
    font-size: 14px;
  }
}
