/**
 * Centralized configuration for project identification and classification
 * This ensures consistent mapping between analytics data and actual projects
 * Backend version - mirrors the frontend configuration
 */

// Experience Projects Configuration
const EXPERIENCE_PROJECTS = [
  {
    id: 3,
    slug: '',
    title: 'MediVision',
    company: 'MediVision',
    analyticsIdentifiers: {
      slugs: ['', 'medivision'],
      titles: ['MediVision'],
      keywords: ['medivision', 'medicine', 'ocr', 'ai']
    }
  },
  {
    id: 1,
    slug: 'frontend-receeto',
    title: 'Frontend Developer Angular',
    company: 'Receeto',
    analyticsIdentifiers: {
      slugs: ['frontend-receeto', 'receeto'],
      titles: ['Frontend Developer Angular', 'Receeto'],
      keywords: ['receeto', 'angular', 'frontend']
    }
  },
  {
    id: 2,
    slug: '3d-ecommerce-platform',
    title: '3D-ecommerce platform UI/UX Designer',
    company: 'DigitalStudio Creative',
    analyticsIdentifiers: {
      slugs: ['3d-ecommerce-platform', '3d-ecommerce'],
      titles: ['3D-ecommerce platform UI/UX Designer', '3D E-commerce'],
      keywords: ['3d', 'ecommerce', 'platform', 'ui/ux']
    }
  }
];

// Portfolio Projects Configuration
const PORTFOLIO_PROJECTS = [
  {
    index: 0,
    id: 'portfolio-0-3d-ecommerce',
    title: '3D Ecommerce',
    href: 'https://threed-e-commerce.onrender.com',
    forSale: true,
    analyticsIdentifiers: {
      projectIds: ['portfolio-0-3d-ecommerce'],
      titles: ['3D Ecommerce'],
      keywords: ['3d', 'ecommerce']
    }
  },
  {
    index: 1,
    id: 'portfolio-1-will-be-deployed-soon',
    title: 'Will be deployed soon.',
    href: '#',
    forSale: false,
    analyticsIdentifiers: {
      projectIds: ['portfolio-1-will-be-deployed-soon'],
      titles: ['Will be deployed soon.'],
      keywords: ['deployed', 'soon']
    }
  },
  {
    index: 2,
    id: 'portfolio-2-professional-portfolio',
    title: 'Professional Portfolio',
    href: 'https://creative-website-jumper.onrender.com',
    forSale: true,
    analyticsIdentifiers: {
      projectIds: ['portfolio-2-professional-portfolio'],
      titles: ['Professional Portfolio'],
      keywords: ['professional', 'portfolio']
    }
  },
  {
    index: 3,
    id: 'portfolio-3-will-be-deployed-soon',
    title: 'Will be deployed soon.',
    href: '#',
    forSale: false,
    analyticsIdentifiers: {
      projectIds: ['portfolio-3-will-be-deployed-soon'],
      titles: ['Will be deployed soon.'],
      keywords: ['deployed', 'soon']
    }
  },
  {
    index: 4,
    id: 'portfolio-4-available',
    title: 'Available',
    href: 'https://flawless-2pqq.onrender.com',
    forSale: true,
    analyticsIdentifiers: {
      projectIds: ['portfolio-4-available'],
      titles: ['Available'],
      keywords: ['available', 'flaw']
    }
  },
  {
    index: 5,
    id: 'portfolio-5-will-be-deployed-soon',
    title: 'Will be deployed soon.',
    href: '#',
    forSale: false,
    analyticsIdentifiers: {
      projectIds: ['portfolio-5-will-be-deployed-soon'],
      titles: ['Will be deployed soon.'],
      keywords: ['deployed', 'soon']
    }
  },
  {
    index: 6,
    id: 'portfolio-6-experience-digital-banking-with-ai',
    title: 'Experience digital banking with AI ',
    href: 'https://hoobank-neon-future.onrender.com',
    forSale: true,
    analyticsIdentifiers: {
      projectIds: ['portfolio-6-experience-digital-banking-with-ai'],
      titles: ['Experience digital banking with AI ', 'HooBank'],
      keywords: ['digital', 'banking', 'ai', 'hoobank']
    }
  }
];

/**
 * Helper functions for project identification
 */

// Find experience project by analytics data
const findExperienceProject = (jobSlug, jobTitle) => {
  return EXPERIENCE_PROJECTS.find(project => {
    // Check by slug
    if (jobSlug && project.analyticsIdentifiers.slugs.includes(jobSlug)) {
      return true;
    }
    
    // Check by title (exact or partial match)
    if (jobTitle) {
      return project.analyticsIdentifiers.titles.some(title =>
        title.toLowerCase().includes(jobTitle.toLowerCase()) ||
        jobTitle.toLowerCase().includes(title.toLowerCase())
      );
    }
    
    return false;
  });
};

// Find portfolio project by analytics data
const findPortfolioProject = (projectId, projectTitle) => {
  return PORTFOLIO_PROJECTS.find(project => {
    // Check by project ID
    if (projectId && project.analyticsIdentifiers.projectIds.includes(projectId)) {
      return true;
    }
    
    // Check by title (exact or partial match)
    if (projectTitle) {
      return project.analyticsIdentifiers.titles.some(title =>
        title.toLowerCase().includes(projectTitle.toLowerCase()) ||
        projectTitle.toLowerCase().includes(title.toLowerCase())
      );
    }
    
    return false;
  });
};

// Validate if analytics data belongs to a valid experience project
const isValidExperienceProject = (jobSlug, jobTitle) => {
  return findExperienceProject(jobSlug, jobTitle) !== undefined;
};

// Validate if analytics data belongs to a valid portfolio project
const isValidPortfolioProject = (projectId, projectTitle) => {
  return findPortfolioProject(projectId, projectTitle) !== undefined;
};

// Get all valid experience project identifiers for database queries
const getExperienceProjectIdentifiers = () => {
  const slugs = EXPERIENCE_PROJECTS.flatMap(p => p.analyticsIdentifiers.slugs);
  const titles = EXPERIENCE_PROJECTS.flatMap(p => p.analyticsIdentifiers.titles);
  return { slugs, titles };
};

// Get all valid portfolio project identifiers for database queries
const getPortfolioProjectIdentifiers = () => {
  const projectIds = PORTFOLIO_PROJECTS.flatMap(p => p.analyticsIdentifiers.projectIds);
  const titles = PORTFOLIO_PROJECTS.flatMap(p => p.analyticsIdentifiers.titles);
  return { projectIds, titles };
};

module.exports = {
  EXPERIENCE_PROJECTS,
  PORTFOLIO_PROJECTS,
  findExperienceProject,
  findPortfolioProject,
  isValidExperienceProject,
  isValidPortfolioProject,
  getExperienceProjectIdentifiers,
  getPortfolioProjectIdentifiers
};
