import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FaRoute, FaClock, FaEye, FaArrowRight, FaPlay, FaPause, FaRedo } from 'react-icons/fa';
import './VisitorMovementSchema.css';

/**
 * VisitorMovementSchema Component
 *
 * Optimized visitor movement tracking with sliding window display:
 * - Shows maximum 5 steps at once to reduce UI clutter and improve performance
 * - Implements sliding window that moves as user progresses through steps
 * - When playing: automatically slides to show current step and upcoming steps
 * - When paused: allows manual navigation through all steps
 * - Performance optimizations: memoized functions, reduced re-renders, CSS containment
 */
const VisitorMovementSchema = ({ visitorData }) => {
  const [animationSpeed, setAnimationSpeed] = useState(2000); // 2 seconds per step
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [movementPath, setMovementPath] = useState([]);
  const [windowStart, setWindowStart] = useState(0); // Start index of the visible window

  const MAX_VISIBLE_STEPS = 5; // Maximum number of steps to display at once

  useEffect(() => {
    if (visitorData && visitorData.visits) {
      // Create movement path from visit history
      const sortedVisits = visitorData.visits
        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
        .map((visit, index) => ({
          ...visit,
          step: index + 1,
          timestamp: new Date(visit.timestamp),
          duration: visit.duration || 0
        }));

      setMovementPath(sortedVisits);
      setCurrentStep(0);
      setWindowStart(0);
    }
  }, [visitorData]);

  // Update window position when current step changes
  useEffect(() => {
    if (movementPath.length > MAX_VISIBLE_STEPS && currentStep >= 0) {
      // Calculate optimal window position
      let newWindowStart = windowStart;

      // If current step is beyond visible window, slide window forward
      if (currentStep >= windowStart + MAX_VISIBLE_STEPS) {
        newWindowStart = currentStep - MAX_VISIBLE_STEPS + 1;
      }
      // If current step is before visible window, slide window backward
      else if (currentStep < windowStart) {
        newWindowStart = currentStep;
      }

      // Ensure window doesn't go beyond bounds
      newWindowStart = Math.max(0, Math.min(newWindowStart, movementPath.length - MAX_VISIBLE_STEPS));

      if (newWindowStart !== windowStart) {
        setWindowStart(newWindowStart);
      }
    }
  }, [currentStep, movementPath.length, windowStart]);

  useEffect(() => {
    let timeoutId;
    let animationFrameId;

    if (isPlaying && movementPath.length > 0) {
      const scheduleNextStep = () => {
        // Use a longer timeout to reduce violation frequency
        timeoutId = setTimeout(() => {
          // Double requestAnimationFrame to ensure smooth updates and reduce violations
          animationFrameId = requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              setCurrentStep(prev => {
                if (prev >= movementPath.length - 1) {
                  setIsPlaying(false);
                  return 0;
                }
                return prev + 1;
              });

              // Schedule next step if still playing
              if (isPlaying) {
                scheduleNextStep();
              }
            });
          });
        }, Math.max(animationSpeed, 100)); // Minimum 100ms to reduce violations
      };

      scheduleNextStep();
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (animationFrameId) cancelAnimationFrame(animationFrameId);
    };
  }, [isPlaying, animationSpeed, movementPath.length]);

  // Memoized functions for better performance
  const formatDuration = useCallback((seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }, []);

  const getSectionColor = useCallback((section) => {
    const colors = {
      'header': '#FF2D55',
      'about': '#007AFF',
      'skills': '#34C759',
      'projects': '#FF9500',
      'timeline': '#AF52DE',
      'contact': '#FF3B30',
      'footer': '#8E8E93'
    };
    return colors[section.toLowerCase()] || '#5AC8FA';
  }, []);

  const handlePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  const handleStepClick = useCallback((stepIndex) => {
    if (stepIndex >= 0 && stepIndex < movementPath.length) {
      setCurrentStep(stepIndex);
      setIsPlaying(false);
    }
  }, [movementPath.length]);

  // Memoized visible steps to optimize rendering
  const visibleSteps = useMemo(() => {
    if (movementPath.length <= MAX_VISIBLE_STEPS) {
      return movementPath;
    }
    return movementPath.slice(windowStart, windowStart + MAX_VISIBLE_STEPS);
  }, [movementPath, windowStart]);

  const handleSpeedChange = useCallback((speed) => {
    setAnimationSpeed(speed);
  }, []);

  const handleReset = useCallback(() => {
    setCurrentStep(0);
    setWindowStart(0);
    setIsPlaying(false);
  }, []);

  if (!visitorData || !movementPath.length) {
    return (
      <div className="visitor-movement-schema">
        <div className="schema-header">
          <h2><FaRoute /> Visitor Movement Schema</h2>
        </div>
        <div className="no-movement-data">
          <p>No movement data available for this visitor.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="visitor-movement-schema">
      <div className="schema-header">
        <h2><FaRoute /> Visitor Movement Schema</h2>
        <div className="schema-controls">
          <div className="playback-controls">
            <button
              className={`play-pause-btn ${isPlaying ? 'playing' : 'paused'}`}
              onClick={handlePlayPause}
            >
              {isPlaying ? <FaPause /> : <FaPlay />}
              {isPlaying ? 'Pause' : 'Play'}
            </button>
            <button
              className="reset-btn"
              onClick={handleReset}
              title="Reset to beginning"
            >
              <FaRedo />
            </button>
          </div>
          <div className="speed-controls">
            <span>Speed:</span>
            <button 
              className={animationSpeed === 3000 ? 'active' : ''}
              onClick={() => handleSpeedChange(3000)}
            >
              Slow
            </button>
            <button 
              className={animationSpeed === 2000 ? 'active' : ''}
              onClick={() => handleSpeedChange(2000)}
            >
              Normal
            </button>
            <button 
              className={animationSpeed === 1000 ? 'active' : ''}
              onClick={() => handleSpeedChange(1000)}
            >
              Fast
            </button>
          </div>
        </div>
      </div>

      <div className="schema-stats">
        <div className="stat-item">
          <FaEye />
          <span>Total Steps: {movementPath.length}</span>
        </div>
        <div className="stat-item">
          <FaClock />
          <span>Total Time: {formatDuration(visitorData.totalDuration)}</span>
        </div>
        <div className="stat-item">
          <FaRoute />
          <span>Sections Visited: {visitorData.sectionsVisited.length}</span>
        </div>
      </div>

      <div className="movement-timeline">
        <div className="timeline-progress">
          <div 
            className="progress-bar"
            style={{ 
              width: `${((currentStep + 1) / movementPath.length) * 100}%` 
            }}
          />
        </div>

        <div className="movement-steps">
          {/* Show window indicator if there are more steps */}
          {movementPath.length > MAX_VISIBLE_STEPS && (
            <div className="steps-window-indicator">
              <span>Showing steps {windowStart + 1}-{Math.min(windowStart + MAX_VISIBLE_STEPS, movementPath.length)} of {movementPath.length}</span>
              {windowStart > 0 && <span className="window-hint">← Previous steps hidden</span>}
              {windowStart + MAX_VISIBLE_STEPS < movementPath.length && <span className="window-hint">More steps ahead →</span>}
            </div>
          )}

          {visibleSteps.map((step, visibleIndex) => {
            const actualIndex = windowStart + visibleIndex;
            const isActive = actualIndex <= currentStep;
            const isCurrent = actualIndex === currentStep;
            const sectionColor = getSectionColor(step.section);

            return (
              <div
                key={actualIndex}
                className={`movement-step ${isActive ? 'active' : ''} ${isCurrent ? 'current' : ''}`}
                onClick={() => handleStepClick(actualIndex)}
              >
                <div className="step-number">{step.step}</div>
                <div
                  className="step-section"
                  style={{ borderColor: sectionColor }}
                >
                  <div className="section-name">{step.section}</div>
                  <div className="section-time">
                    {step.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="section-duration">
                    <FaClock /> {formatDuration(step.duration)}
                  </div>
                </div>
                {actualIndex < movementPath.length - 1 && (
                  <div className="step-arrow">
                    <FaArrowRight />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <div className="current-step-details">
        {movementPath[currentStep] && (
          <div className="step-details-card">
            <h3>Step {currentStep + 1} Details</h3>
            <div className="details-grid">
              <div className="detail-item">
                <span className="detail-label">Section:</span>
                <span 
                  className="detail-value section-badge"
                  style={{ backgroundColor: getSectionColor(movementPath[currentStep].section) }}
                >
                  {movementPath[currentStep].section}
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Time:</span>
                <span className="detail-value">
                  {movementPath[currentStep].timestamp.toLocaleString()}
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Duration:</span>
                <span className="detail-value">
                  {formatDuration(movementPath[currentStep].duration)}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VisitorMovementSchema;
