/* Visitor Movement Schema Styles */
.visitor-movement-schema {
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
  margin-bottom: 30px;
}

.schema-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.schema-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 1.8rem;
  margin: 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.schema-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.play-pause-btn {
  background: linear-gradient(135deg, #FF2D55 0%, #FF6B6B 100%);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);
}

.play-pause-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 45, 85, 0.4);
}

.play-pause-btn.playing {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.speed-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.speed-controls span {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.speed-controls button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 6px 12px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.speed-controls button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.speed-controls button.active {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-color: #007AFF;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.schema-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 18px;
  border-radius: 12px;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(5px);
}

.stat-item svg {
  color: #FF2D55;
  font-size: 0.9rem;
}

.movement-timeline {
  position: relative;
  margin-bottom: 30px;
}

.timeline-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin-bottom: 20px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #FF2D55 0%, #FF6B6B 50%, #FF9500 100%);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.movement-steps {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.steps-window-indicator {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 12px 16px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.window-hint {
  color: #FF2D55;
  font-size: 0.8rem;
  font-weight: 600;
  opacity: 0.8;
}

.movement-step {
  display: flex;
  align-items: center;
  gap: 15px;
  opacity: 0.4;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transform: translateX(0);
  will-change: transform, opacity;
}

.movement-step.active {
  opacity: 1;
}

.movement-step.current {
  opacity: 1;
  transform: scale(1.02) translateX(0);
  box-shadow: 0 8px 25px rgba(255, 45, 85, 0.15);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF2D55 0%, #FF6B6B 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(255, 45, 85, 0.3);
}

.movement-step.active .step-number {
  box-shadow: 0 6px 20px rgba(255, 45, 85, 0.5);
}

.step-section {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px 20px;
  flex: 1;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  will-change: border-color, box-shadow;
  contain: layout style;
}

.movement-step.active .step-section {
  border-width: 2px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.section-name {
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 5px;
  text-transform: capitalize;
}

.section-time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.section-duration {
  color: #FF2D55;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.step-arrow {
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.2rem;
  margin: 0 10px;
}

.movement-step.active .step-arrow {
  color: #FF2D55;
}

.current-step-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.step-details-card h3 {
  color: #fff;
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255,255,255,0.05);
  border-radius: 8px;
  border: 1px solid rgba(255,255,255,0.1);
}

.detail-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.detail-value {
  font-weight: 700;
  color: #fff;
}

.section-badge {
  padding: 4px 12px;
  border-radius: 15px;
  color: white !important;
  font-size: 0.8rem;
  font-weight: 600;
}

.no-movement-data {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .visitor-movement-schema {
    padding: 20px;
  }
  
  .schema-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .schema-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .schema-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .movement-step {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .step-arrow {
    transform: rotate(90deg);
    margin: 5px 0;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }

  .steps-window-indicator {
    flex-direction: column;
    gap: 8px;
    padding: 10px 12px;
    font-size: 0.8rem;
  }

  .window-hint {
    font-size: 0.75rem;
  }
}
