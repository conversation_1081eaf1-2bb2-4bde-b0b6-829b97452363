/*
  # Disable Privacy Sandbox APIs to prevent console messages
  Permissions-Policy: browsing-topics=(), interest-cohort=(), attribution-reporting=(), trust-tokens=()
  
  # Additional security headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Cache control for static assets
  Cache-Control: public, max-age=31536000

# Specific cache control for HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Specific cache control for service worker
/service-worker.js
  Cache-Control: no-cache, no-store, must-revalidate
